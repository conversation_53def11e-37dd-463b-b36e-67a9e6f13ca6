"""
Unit tests for Turkish demo request models.

These tests verify that Turkish demo request models work correctly and use the Turkish database.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, timedelta
from .models_tr import DemoRequestTR
from core.tests import TurkishTestSetupMixin


class DemoRequestTRModelTest(TurkishTestSetupMixin, TestCase):
    """Test DemoRequestTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.demo_request_data = {
            'first_name': 'Ahmet',
            'last_name': '<PERSON>ılma<PERSON>',
            'email': '<EMAIL>',
            'phone': '+905551234567',
            'company_name': 'Örnek Şirket',
            'company_website': 'https://ornekfirma.com',
            'company_size': '11-50',
            'job_title': 'CTO',
            'project_type': 'web_app',
            'budget_range': '50k_100k',
            'timeline': '2_3_months',
            'project_description': 'E-ticaret web sitesi geliştirmek istiyoruz.',
            'specific_requirements': 'Mobil uyumlu olmalı ve ödeme entegrasyonu olmalı.',
            'preferred_demo_date': date.today() + timedelta(days=7),
            'preferred_demo_time': 'afternoon',
            'how_did_you_hear': 'google_search'
        }
    
    def test_create_demo_request_tr(self):
        """Test creating DemoRequestTR instance."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        
        self.assertEqual(demo.first_name, 'Ahmet')
        self.assertEqual(demo.last_name, 'Yılmaz')
        self.assertEqual(demo.email, '<EMAIL>')
        self.assertEqual(demo.company_name, 'Örnek Şirket')
        self.assertEqual(demo.project_type, 'web_app')
        self.assertEqual(demo.status, 'new')  # Default status
        self.assertIsNotNone(demo.created_at)
        self.assertIsNotNone(demo.updated_at)
    
    def test_demo_request_tr_uses_turkish_database(self):
        """Test that DemoRequestTR uses Turkish database."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        self.assertEqual(demo._state.db, 'turkish')
    
    def test_demo_request_tr_str_representation(self):
        """Test string representation of DemoRequestTR."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        expected_str = f"{demo.first_name} {demo.last_name} - {demo.company_name} ({demo.get_status_display()})"
        self.assertEqual(str(demo), expected_str)
    
    def test_full_name_property(self):
        """Test full_name property."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        self.assertEqual(demo.full_name, 'Ahmet Yılmaz')
    
    def test_is_high_value_lead_property(self):
        """Test is_high_value_lead property."""
        # Test high value budget
        high_value_data = self.demo_request_data.copy()
        high_value_data['budget_range'] = '500k_plus'
        high_value_data['email'] = '<EMAIL>'
        demo1 = DemoRequestTR.objects.create(**high_value_data)
        self.assertTrue(demo1.is_high_value_lead)
        
        # Test large company
        large_company_data = self.demo_request_data.copy()
        large_company_data['company_size'] = '1000+'
        large_company_data['budget_range'] = '10k_25k'  # Lower budget
        large_company_data['email'] = '<EMAIL>'
        demo2 = DemoRequestTR.objects.create(**large_company_data)
        self.assertTrue(demo2.is_high_value_lead)
        
        # Test regular lead
        regular_data = self.demo_request_data.copy()
        regular_data['budget_range'] = '10k_25k'
        regular_data['company_size'] = '1-10'
        regular_data['email'] = '<EMAIL>'
        demo3 = DemoRequestTR.objects.create(**regular_data)
        self.assertFalse(demo3.is_high_value_lead)
    
    def test_urgency_level_property(self):
        """Test urgency_level property."""
        test_cases = [
            ('asap', 'Yüksek'),
            ('1_month', 'Yüksek'),
            ('2_3_months', 'Orta'),
            ('3_6_months', 'Düşük'),
            ('6_months_plus', 'Düşük'),
            ('flexible', 'Düşük')
        ]
        
        for timeline, expected_urgency in test_cases:
            demo_data = self.demo_request_data.copy()
            demo_data['timeline'] = timeline
            demo_data['email'] = f'{timeline}@test.com'
            demo = DemoRequestTR.objects.create(**demo_data)
            
            self.assertEqual(demo.urgency_level, expected_urgency)
            
            # Clean up for next iteration
            demo.delete()
    
    def test_contact_info_property(self):
        """Test contact_info property."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        contact_info = demo.contact_info
        
        expected_keys = ['name', 'email', 'phone', 'company', 'title']
        for key in expected_keys:
            self.assertIn(key, contact_info)
        
        self.assertEqual(contact_info['name'], 'Ahmet Yılmaz')
        self.assertEqual(contact_info['email'], '<EMAIL>')
        self.assertEqual(contact_info['phone'], '+905551234567')
        self.assertEqual(contact_info['company'], 'Örnek Şirket')
        self.assertEqual(contact_info['title'], 'CTO')
    
    def test_get_status_color_method(self):
        """Test get_status_color method."""
        test_cases = [
            ('new', '#007bff'),
            ('contacted', '#17a2b8'),
            ('demo_scheduled', '#ffc107'),
            ('demo_completed', '#6f42c1'),
            ('proposal_sent', '#fd7e14'),
            ('negotiating', '#e83e8c'),
            ('won', '#28a745'),
            ('lost', '#dc3545'),
            ('on_hold', '#6c757d')
        ]
        
        for status, expected_color in test_cases:
            demo_data = self.demo_request_data.copy()
            demo_data['status'] = status
            demo_data['email'] = f'{status}@test.com'
            demo = DemoRequestTR.objects.create(**demo_data)
            
            self.assertEqual(demo.get_status_color(), expected_color)
            
            # Clean up for next iteration
            demo.delete()
    
    def test_get_priority_score_method(self):
        """Test get_priority_score method."""
        # Test high priority (high budget + large company + urgent timeline)
        high_priority_data = self.demo_request_data.copy()
        high_priority_data['budget_range'] = '500k_plus'  # 10 points
        high_priority_data['company_size'] = '1000+'      # 10 points
        high_priority_data['timeline'] = 'asap'           # 10 points
        high_priority_data['email'] = '<EMAIL>'
        demo1 = DemoRequestTR.objects.create(**high_priority_data)
        
        score1 = demo1.get_priority_score()
        self.assertEqual(score1, 30)  # 10 + 10 + 10
        
        # Test low priority
        low_priority_data = self.demo_request_data.copy()
        low_priority_data['budget_range'] = 'under_10k'   # 1 point
        low_priority_data['company_size'] = '1-10'        # 1 point
        low_priority_data['timeline'] = '6_months_plus'   # 1 point
        low_priority_data['email'] = '<EMAIL>'
        demo2 = DemoRequestTR.objects.create(**low_priority_data)
        
        score2 = demo2.get_priority_score()
        self.assertEqual(score2, 3)  # 1 + 1 + 1
    
    def test_get_leads_by_status_class_method(self):
        """Test get_leads_by_status class method."""
        # Create demo requests with different statuses
        statuses = ['new', 'contacted', 'demo_scheduled', 'won']
        for i, status in enumerate(statuses):
            demo_data = self.demo_request_data.copy()
            demo_data['status'] = status
            demo_data['email'] = f'{status}{i}@test.com'
            DemoRequestTR.objects.create(**demo_data)
        
        leads_by_status = DemoRequestTR.get_leads_by_status()
        
        # Should have entries for each status
        status_counts = {item['status']: item['count'] for item in leads_by_status}
        
        for status in statuses:
            self.assertIn(status, status_counts)
            self.assertEqual(status_counts[status], 1)
    
    def test_get_high_priority_leads_class_method(self):
        """Test get_high_priority_leads class method."""
        # Create high priority lead
        high_priority_data = self.demo_request_data.copy()
        high_priority_data['budget_range'] = '500k_plus'
        high_priority_data['company_size'] = '1000+'
        high_priority_data['timeline'] = 'asap'
        high_priority_data['status'] = 'new'
        high_priority_data['email'] = '<EMAIL>'
        high_priority_demo = DemoRequestTR.objects.create(**high_priority_data)
        
        # Create low priority lead
        low_priority_data = self.demo_request_data.copy()
        low_priority_data['budget_range'] = 'under_10k'
        low_priority_data['company_size'] = '1-10'
        low_priority_data['timeline'] = '6_months_plus'
        low_priority_data['status'] = 'new'
        low_priority_data['email'] = '<EMAIL>'
        low_priority_demo = DemoRequestTR.objects.create(**low_priority_data)
        
        # Create completed lead (should be excluded)
        completed_data = self.demo_request_data.copy()
        completed_data['budget_range'] = '500k_plus'
        completed_data['status'] = 'won'
        completed_data['email'] = '<EMAIL>'
        completed_demo = DemoRequestTR.objects.create(**completed_data)
        
        high_priority_leads = DemoRequestTR.get_high_priority_leads(limit=5)
        
        # High priority lead should be first
        self.assertIn(high_priority_demo, high_priority_leads)
        self.assertIn(low_priority_demo, high_priority_leads)
        self.assertNotIn(completed_demo, high_priority_leads)  # Excluded due to status
        
        # High priority should come first
        lead_ids = [lead.id for lead in high_priority_leads]
        self.assertLess(lead_ids.index(high_priority_demo.id), lead_ids.index(low_priority_demo.id))
    
    def test_demo_request_validation(self):
        """Test DemoRequestTR validation."""
        # Test that demo date cannot be in the past
        past_date_data = self.demo_request_data.copy()
        past_date_data['preferred_demo_date'] = date.today() - timedelta(days=1)
        past_date_data['email'] = '<EMAIL>'
        
        demo = DemoRequestTR(**past_date_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()
    
    def test_phone_validation(self):
        """Test phone number validation."""
        # Test valid phone numbers
        valid_phones = ['+905551234567', '05551234567', '+1234567890']
        
        for phone in valid_phones:
            demo_data = self.demo_request_data.copy()
            demo_data['phone'] = phone
            demo_data['email'] = f'{phone.replace("+", "").replace(" ", "")}@test.com'
            demo = DemoRequestTR(**demo_data)
            
            try:
                demo.full_clean()
            except ValidationError:
                self.fail(f"Valid phone number {phone} failed validation")
            
            # Clean up
            if demo.pk:
                demo.delete()
    
    def test_email_validation(self):
        """Test email validation."""
        # Test invalid email
        invalid_data = self.demo_request_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        demo = DemoRequestTR(**invalid_data)
        with self.assertRaises(ValidationError):
            demo.full_clean()
    
    def test_turkish_model_manager(self):
        """Test that DemoRequestTR uses TurkishModelManager."""
        demo = DemoRequestTR.objects.create(**self.demo_request_data)
        
        # Test that queryset uses Turkish database
        queryset = DemoRequestTR.objects.all()
        self.assertEqual(queryset.db, 'turkish')
        
        # Test get_or_create uses Turkish database
        demo2, created = DemoRequestTR.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'New',
                'last_name': 'Demo',
                'company_name': 'New Company',
                'project_description': 'New project'
            }
        )
        
        self.assertTrue(created)
        self.assertEqual(demo2._state.db, 'turkish')
