from django.test import TestCase
from django.db import connections
from django.core.management import call_command


class TurkishTestSetupMixin:
    """
    Mixin to set up Turkish database tables for testing.

    This ensures that Turkish models can be tested properly by creating
    the necessary tables in the test Turkish database.
    """

    @classmethod
    def setUpClass(cls):
        """Set up Turkish database tables for testing."""
        super().setUpClass()
        cls._create_turkish_tables()

    @classmethod
    def _create_turkish_tables(cls):
        """Create Turkish database tables in the test database."""
        turkish_db = connections['turkish']

        # SQL statements for creating Turkish tables
        sql_statements = {
            'about_us_tr': '''
                CREATE TABLE IF NOT EXISTS about_us_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    description TEXT NOT NULL,
                    main_image VARCHAR(500),
                    secondary_image VARCHAR(500),
                    company_name VARCHAR(100) NOT NULL,
                    established_year INTEGER,
                    mission_statement TEXT,
                    vision_statement TEXT,
                    "values" TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'services_tr': '''
                CREATE TABLE IF NOT EXISTS services_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(150) NOT NULL,
                    category VARCHAR(50) NOT NULL,
                    short_description VARCHAR(300),
                    full_description TEXT,
                    icon VARCHAR(100),
                    image VARCHAR(500),
                    features TEXT,
                    price_range VARCHAR(50),
                    delivery_time VARCHAR(100),
                    is_featured BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'additional_services_tr': '''
                CREATE TABLE IF NOT EXISTS additional_services_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(150) NOT NULL,
                    subtitle VARCHAR(200),
                    description TEXT,
                    icon VARCHAR(100),
                    image VARCHAR(500),
                    features TEXT,
                    price_info VARCHAR(100),
                    is_popular BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'featured_resources_tr': '''
                CREATE TABLE IF NOT EXISTS featured_resources_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    image_url VARCHAR(500) NOT NULL,
                    resource_type VARCHAR(50) NOT NULL,
                    content_url VARCHAR(500) NOT NULL,
                    external_link BOOLEAN NOT NULL DEFAULT 0,
                    reading_time INTEGER,
                    difficulty_level VARCHAR(20),
                    tags TEXT,
                    author VARCHAR(100),
                    published_date DATE,
                    is_featured BOOLEAN NOT NULL DEFAULT 1,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    view_count INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'social_media_links_tr': '''
                CREATE TABLE IF NOT EXISTS social_media_links_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform VARCHAR(50) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    url VARCHAR(500) NOT NULL,
                    icon_class VARCHAR(100),
                    icon_svg TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    follower_count INTEGER,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'team_members_tr': '''
                CREATE TABLE IF NOT EXISTS team_members_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    full_name VARCHAR(200) NOT NULL,
                    position VARCHAR(100) NOT NULL,
                    bio TEXT,
                    profile_image VARCHAR(500),
                    email VARCHAR(254),
                    phone VARCHAR(20),
                    linkedin_url VARCHAR(500),
                    github_url VARCHAR(500),
                    twitter_url VARCHAR(500),
                    skills TEXT,
                    years_of_experience INTEGER,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'technology_categories_tr': '''
                CREATE TABLE IF NOT EXISTS technology_categories_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(200) NOT NULL,
                    description TEXT,
                    icon VARCHAR(100),
                    color_code VARCHAR(7),
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            ''',
            'technologies_tr': '''
                CREATE TABLE IF NOT EXISTS technologies_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) NOT NULL,
                    logo_url VARCHAR(500),
                    category_id INTEGER NOT NULL,
                    description TEXT,
                    proficiency_level VARCHAR(20) NOT NULL DEFAULT 'intermediate',
                    years_experience INTEGER,
                    is_featured BOOLEAN NOT NULL DEFAULT 0,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    display_order INTEGER NOT NULL DEFAULT 0,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (category_id) REFERENCES technology_categories_tr (id)
                )
            ''',
            'demo_requests_tr': '''
                CREATE TABLE IF NOT EXISTS demo_requests_tr (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    email VARCHAR(190) NOT NULL,
                    phone VARCHAR(20),
                    company_name VARCHAR(100) NOT NULL,
                    company_website VARCHAR(200),
                    company_size VARCHAR(20),
                    job_title VARCHAR(100),
                    project_type VARCHAR(100),
                    budget_range VARCHAR(50),
                    timeline VARCHAR(50),
                    project_description TEXT NOT NULL,
                    specific_requirements TEXT,
                    preferred_demo_date DATE,
                    preferred_demo_time VARCHAR(20),
                    how_did_you_hear VARCHAR(100),
                    status VARCHAR(20) NOT NULL DEFAULT 'new',
                    notes TEXT,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL
                )
            '''
        }

        with turkish_db.cursor() as cursor:
            for table_name, sql in sql_statements.items():
                try:
                    cursor.execute(sql)
                except Exception as e:
                    print(f"Error creating table {table_name}: {e}")


# Create your tests here.
